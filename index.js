// TREVIA Landing Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            const icon = mobileMenuBtn.querySelector('i');
            if (mobileMenu.classList.contains('hidden')) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            } else {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            }
        });
        
        // Close mobile menu when clicking on links
        const mobileLinks = mobileMenu.querySelectorAll('a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
                const icon = mobileMenuBtn.querySelector('i');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            });
        });
    }
    
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Project modal functionality
    const projectCards = document.querySelectorAll('.project-card');
    const modal = document.getElementById('project-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');
    const closeModal = document.getElementById('close-modal');
    const demoBtn = document.getElementById('demo-btn');
    
    // Project data
    const projectData = {
        'erp-distribuicao': {
            title: 'ERP para Distribuição',
            description: `
                <div class="space-y-4">
                    <p>Sistema completo de gestão empresarial desenvolvido especificamente para empresas de distribuição em Angola.</p>
                    
                    <h4 class="text-lg font-semibold text-gray-900">Funcionalidades Principais:</h4>
                    <ul class="list-disc list-inside space-y-2 text-gray-600">
                        <li>Gestão completa de estoque com controle de lotes e validades</li>
                        <li>Sistema de vendas integrado com emissão de faturas</li>
                        <li>Controle de rotas de entrega e logística</li>
                        <li>Relatórios financeiros em tempo real</li>
                        <li>Gestão de fornecedores e compras</li>
                        <li>Dashboard executivo com KPIs</li>
                    </ul>
                    
                    <h4 class="text-lg font-semibold text-gray-900">Resultados Alcançados:</h4>
                    <ul class="list-disc list-inside space-y-2 text-gray-600">
                        <li>70% de redução no tempo de geração de relatórios</li>
                        <li>95% de precisão no controle de estoque</li>
                        <li>40% de melhoria na eficiência das entregas</li>
                    </ul>
                </div>
            `,
            demoUrl: 'https://demo-erp.trevia.ao'
        },
        'crm-imobiliaria': {
            title: 'CRM Imobiliário',
            description: `
                <div class="space-y-4">
                    <p>Sistema de gestão de relacionamento com clientes desenvolvido para empresa do setor imobiliário.</p>
                    
                    <h4 class="text-lg font-semibold text-gray-900">Funcionalidades Principais:</h4>
                    <ul class="list-disc list-inside space-y-2 text-gray-600">
                        <li>Cadastro completo de clientes e propriedades</li>
                        <li>Pipeline de vendas com acompanhamento de negociações</li>
                        <li>Sistema de agendamento de visitas</li>
                        <li>Geração automática de contratos</li>
                        <li>Relatórios de performance de vendas</li>
                        <li>Integração com WhatsApp para comunicação</li>
                    </ul>
                    
                    <h4 class="text-lg font-semibold text-gray-900">Resultados Alcançados:</h4>
                    <ul class="list-disc list-inside space-y-2 text-gray-600">
                        <li>45% de aumento nas vendas</li>
                        <li>60% de melhoria no follow-up de clientes</li>
                        <li>80% de redução no tempo de geração de contratos</li>
                    </ul>
                </div>
            `,
            demoUrl: 'https://demo-crm.trevia.ao'
        },
        'sistema-restaurante': {
            title: 'Sistema para Restaurante',
            description: `
                <div class="space-y-4">
                    <p>Sistema completo de gestão para restaurante com IA preditiva para otimização de estoque.</p>
                    
                    <h4 class="text-lg font-semibold text-gray-900">Funcionalidades Principais:</h4>
                    <ul class="list-disc list-inside space-y-2 text-gray-600">
                        <li>Sistema de pedidos com interface touch</li>
                        <li>Gestão de estoque de ingredientes</li>
                        <li>IA preditiva para previsão de demanda</li>
                        <li>Controle de mesas e reservas</li>
                        <li>Relatórios de vendas e análise de pratos</li>
                        <li>Integração com delivery</li>
                    </ul>
                    
                    <h4 class="text-lg font-semibold text-gray-900">Resultados Alcançados:</h4>
                    <ul class="list-disc list-inside space-y-2 text-gray-600">
                        <li>30% de redução no desperdício de ingredientes</li>
                        <li>50% de melhoria na velocidade de atendimento</li>
                        <li>25% de aumento na satisfação dos clientes</li>
                    </ul>
                </div>
            `,
            demoUrl: 'https://demo-restaurante.trevia.ao'
        }
    };
    
    // Add click event to project cards
    projectCards.forEach(card => {
        card.addEventListener('click', function() {
            const projectKey = this.getAttribute('data-project');
            const project = projectData[projectKey];
            
            if (project) {
                modalTitle.textContent = project.title;
                modalContent.innerHTML = project.description;
                demoBtn.onclick = () => window.open(project.demoUrl, '_blank');
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        });
    });
    
    // Close modal functionality
    function closeProjectModal() {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
    
    if (closeModal) {
        closeModal.addEventListener('click', closeProjectModal);
    }
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeProjectModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
            closeProjectModal();
        }
    });
    
    // CTA button functionality
    const ctaButtons = document.querySelectorAll('button');
    ctaButtons.forEach(button => {
        if (button.textContent.includes('Demonstração') || button.textContent.includes('Solicitar')) {
            button.addEventListener('click', function() {
                // Simulate form submission or redirect to contact
                alert('Obrigado pelo interesse! Em breve entraremos em contato para agendar sua demonstração gratuita.');
                // In a real implementation, this would open a contact form or redirect to a contact page
            });
        }
        
        if (button.textContent.includes('WhatsApp')) {
            button.addEventListener('click', function() {
                // Open WhatsApp with pre-filled message
                const message = encodeURIComponent('Olá! Gostaria de saber mais sobre os sistemas da TREVIA.');
                window.open(`https://wa.me/244900000000?text=${message}`, '_blank');
            });
        }
    });
    
    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.card-hover, .project-card, .bg-gray-50 > div');
    animateElements.forEach(el => {
        observer.observe(el);
    });
    
    // Header background on scroll
    const header = document.querySelector('header');
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.classList.add('bg-white');
            header.classList.remove('bg-white/95');
        } else {
            header.classList.remove('bg-white');
            header.classList.add('bg-white/95');
        }
    });
    
    // Stats counter animation
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        
        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        }
        
        updateCounter();
    }
    
    // Animate stats when they come into view
    const statsObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statNumber = entry.target.textContent.replace(/\D/g, '');
                if (statNumber) {
                    animateCounter(entry.target, parseInt(statNumber));
                }
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    const statElements = document.querySelectorAll('.text-4xl.font-bold.text-trevia-orange');
    statElements.forEach(el => {
        statsObserver.observe(el);
    });
    
    // Form validation (if forms are added later)
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
    // Utility function for showing notifications
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 'bg-red-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    
    console.log('TREVIA Landing Page loaded successfully!');
});
