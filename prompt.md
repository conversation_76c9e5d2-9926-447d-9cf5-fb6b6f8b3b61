### Você é um desenvolvedor front-end com foco em Tailwind CSS. Crie uma landing page moderna e responsiva para a empresa angolana TREVIA – Comércio e Prestação de Serviços, com foco exclusivo na venda de sistemas e softwares personalizados.

A página deve conter:

Header com o logotipo da TREVIA e menu de navegação fixo.

Hero Section com título forte e CTA "Solicite uma Demonstração".

Seção explicando os tipos de sistemas desenvolvidos: ERPs, CRMs, sistemas de gestão, softwares sob medida, automação com inteligência artificial.

Seção de diferenciais: atendimento personalizado, IA integrada, suporte local em Angola.

Seção de prova social com feedback de clientes (testemunhos).

Galeria de projetos entregues: miniaturas que, ao clicar, aumentam em modal com link externo para visualizar o sistema funcionando.

Rodapé com contatos e links úteis.

Observações:

Use Tailwind CSS para todo o layout e responsividade.

Cores principais inspiradas no logotipo da TREVIA (azul, laranja, verde, cinza), com suporte a fundo branco/preto para contraste e legibilidade.

Design com foco em conversão e clareza da proposta.
