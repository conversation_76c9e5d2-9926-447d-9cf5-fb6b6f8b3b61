/* Custom CSS for TREVIA Landing Page */

/* Smooth scrolling for the entire page */
html {
    scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Animation classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-pulse-slow {
    animation: pulse 2s infinite;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

/* Custom hover effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Custom gradient backgrounds */
.gradient-blue {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.gradient-orange {
    background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
}

.gradient-green {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
}

/* Custom button styles */
.btn-primary {
    background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(234, 88, 12, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(234, 88, 12, 0.4);
}

.btn-secondary {
    background: transparent;
    border: 2px solid #2563eb;
    color: #2563eb;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #2563eb;
    color: white;
    transform: translateY(-2px);
}

/* Custom card styles */
.card-hover {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.card-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #e5e7eb;
}

/* Custom text effects */
.text-gradient {
    background: linear-gradient(135deg, #2563eb 0%, #ea580c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Loading animation for images */
.img-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #2563eb;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1d4ed8;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
        line-height: 1.5;
    }
    
    .section-title {
        font-size: 2rem;
        line-height: 1.3;
    }
    
    .card-padding {
        padding: 1.5rem;
    }
}

/* Focus states for accessibility */
button:focus,
a:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

/* Custom modal styles */
.modal-backdrop {
    backdrop-filter: blur(4px);
}

.modal-content {
    max-height: 90vh;
    overflow-y: auto;
}

/* Project card hover effects */
.project-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.project-card:hover .project-icon {
    transform: scale(1.1);
}

.project-icon {
    transition: transform 0.3s ease;
}

/* Stats counter animation */
.stat-number {
    font-variant-numeric: tabular-nums;
}

/* Custom spacing utilities */
.section-padding {
    padding-top: 5rem;
    padding-bottom: 5rem;
}

@media (max-width: 768px) {
    .section-padding {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
}

/* Header backdrop blur effect */
.header-backdrop {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Custom form styles */
.form-input {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px 16px;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    border-color: #2563eb;
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2563eb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
}
